// configure dotenv FIRST before any other imports
import dotenv from "dotenv";
dotenv.config();

import express from "express";
import chatRoutes from "./router/chat";
//start application
const app = express();

//configuration
app.use(express.json());
//chat routes
app.use("/api/chat", chatRoutes);

// configure live port
const port = process.env.PORT || 6000;

//start server
app.listen(port, () => {
  console.log(`Server is running on port ${port}`);
});
