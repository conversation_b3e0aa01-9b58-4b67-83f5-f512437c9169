import { transporter } from "../config";

export const sendMail = async (
  to: string,
  subject: string,
  content: string
) => {
  try {
    const sender = {
      address: "<EMAIL>",
      name: "mcp_Test",
    };
    const recipients = [to];

    console.log("Attempting to send email to:", to);
    console.log("Using sender:", sender);

    const result = await transporter.sendMail({
      from: sender,
      to: recipients,
      subject: subject,
      text: content,
      category: "Integration Test",
      sandbox: true,
    });

    console.log("Email sent successfully:", result);
    return result;
  } catch (err) {
    console.error("Email error:", err);
    throw new Error(`Failed to send email: ${err}`);
  }
};
