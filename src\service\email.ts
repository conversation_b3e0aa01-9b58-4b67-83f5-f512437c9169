import { transporter } from "../config";

export const sendMail = async (
  to: string,
  subject: string,
  content: string
) => {
  try {
    const sender = {
      address: "<EMAIL>",
      name: "mcp_Test",
    };
    const recipients = [to];

    transporter
      .sendMail({
        from: sender,
        to: recipients,
        subject: subject,
        text: content,
        category: "Integration Test",
        sandbox: true,
      })
      .then((data) => {
        return data;
      });
  } catch (err) {
    console.error("Email error:", err);
    throw new Error("Failed to send email");
  }
};
