const FormData = require("form-data");
const fs = require("fs");
const axios = require("axios");
const path = require("path");

async function testAskEndpoint() {
  try {
    // Create a simple test DOCX file content for testing
    // You should replace this with an actual .docx file path
    const testFilePath = path.join(__dirname, "test-resume.docx");

    if (!fs.existsSync(testFilePath)) {
      console.log(
        "❌ Test file not found. Please create a test-resume.docx file in the project root."
      );
      console.log("For now, testing without file upload...");
      return;
    }

    const form = new FormData();
    form.append("question", "What is my work experience?");
    form.append("resume", fs.createReadStream(testFilePath));

    console.log("🚀 Testing /api/chat/ask endpoint...");

    const response = await axios.post(
      "http://127.0.0.1:6000/api/chat/ask",
      form,
      {
        headers: {
          ...form.getHeaders(),
        },
        timeout: 30000, // 30 second timeout
      }
    );

    console.log("✅ API Response:");
    console.log(JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error("❌ API Test failed:");
    if (error.response) {
      console.error("Status:", error.response.status);
      console.error("Data:", error.response.data);
    } else {
      console.error("Error:", error.message);
    }
  }
}

// Test without file first
async function testWithoutFile() {
  try {
    console.log("🚀 Testing server health...");
    const response = await axios.get("http://127.0.0.1:6000/api/chat/chat");
    console.log("✅ Server is responding:", response.data);
  } catch (error) {
    console.error("❌ Server health check failed:", error.message);
  }
}

async function runTests() {
  await testWithoutFile();
  await testAskEndpoint();
}

runTests();
